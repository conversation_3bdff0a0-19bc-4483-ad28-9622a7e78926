<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.perennialsys.peppol.validation</groupId>
    <artifactId>peppol_validation_service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>Peppol Validation</name>
    <description>Peppol Validation</description>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>
    <properties>
        <java.version>17</java.version>
        <jaxb-plugin.group>org.jvnet.jaxb</jaxb-plugin.group>
        <jaxb-plugin.artifact>jaxb-maven-plugin</jaxb-plugin.artifact>
        <jaxb-plugin.version>4.0.8</jaxb-plugin.version>
        <ph-jaxb-plugin.version>4.0.3</ph-jaxb-plugin.version>
        <ph-schematron.version>8.0.6</ph-schematron.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central</name>
            <url>https://repo1.maven.org/maven2</url>
        </repository>
        <repository>
            <id>helger-maven</id>
            <name>Helger Maven Repository</name>
            <url>https://repo.helger.com/maven2/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
            <version>6.2.6</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>32.1.2-jre</version>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.0.2</version>
        </dependency>

        <!--PHIVE Dependencies-->
        <dependency>
            <groupId>com.helger.phive</groupId>
            <artifactId>phive-xml</artifactId>
            <version>10.1.1</version><!--9.2.2-->
        </dependency>

        <dependency>
            <groupId>com.helger.phive</groupId>
            <artifactId>phive-result</artifactId>
            <version>10.1.1</version><!--9.2.2-->
        </dependency>

        <dependency>
            <groupId>com.helger.phive.rules</groupId>
            <artifactId>phive-rules-peppol</artifactId>
            <version>3.2.13</version>
        </dependency>

        <dependency>
            <groupId>com.helger</groupId>
            <artifactId>ph-sbdh</artifactId>
            <version>5.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.helger.peppol</groupId>
            <artifactId>peppol-commons</artifactId>
            <version>9.5.1</version>
        </dependency>
        <dependency>
            <groupId>com.helger.peppol</groupId>
            <artifactId>peppol-sbdh</artifactId>
            <version>9.5.1</version>
        </dependency>

        <dependency>
            <groupId>com.helger.diver</groupId>
            <artifactId>ph-diver-repo-http</artifactId>
            <version>3.0.1</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.helger/ph-commons -->
        <dependency>
            <groupId>com.helger</groupId>
            <artifactId>ph-commons</artifactId>
            <version>9.5.5</version>
        </dependency>

        <!--PHIVE Dependencies-->

        <dependency>
            <groupId>com.helger.diver</groupId>
            <artifactId>ph-diver-api</artifactId>
            <version>3.0.1</version>
        </dependency>

        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-rt</artifactId>
            <version>4.0.2</version>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
            <version>4.0.1</version>
        </dependency>

        <dependency>
            <groupId>jakarta.activation</groupId>
            <artifactId>jakarta.activation-api</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>4.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.15.2</version>
        </dependency>

        <!-- JPA and MySQL dependencies removed since we're using MongoDB -->
        <!--
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>
        -->

        <!-- MongoDB Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Automatic-Module-Name>com.helger.phive.peppol</Automatic-Module-Name>
                        <Export-Package>
                            com.helger.phive.peppol
                        </Export-Package>
                        <Import-Package>!javax.annotation.*,*</Import-Package>
                    </instructions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.helger.maven</groupId>
                <artifactId>ph-schematron-maven-plugin</artifactId>
                <version>${ph-schematron.version}</version>
            </plugin>
        </plugins>
        <finalName>peppol-validation-service</finalName>
    </build>
</project>
