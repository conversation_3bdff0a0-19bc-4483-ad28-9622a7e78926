package com.perennialsys.peppol.validation.xsd.service;

import com.perennialsys.peppol.validation.xsd.dto.XsdValidationRequest;
import com.perennialsys.peppol.validation.xsd.dto.XsdValidationResponse;
import com.perennialsys.peppol.validation.xsd.service.impl.XsdValidationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for XSD validation service.
 * 
 * <AUTHOR>
 * @since 1.0
 * @version 1.0
 */
@SpringBootTest
class XsdValidationServiceTest {

    private IXsdValidationService xsdValidationService;

    @BeforeEach
    void setUp() {
        xsdValidationService = new XsdValidationServiceImpl();
    }

    @Test
    void testValidXmlAgainstXsd() throws Exception {
        // Valid XML content
        String xmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <person>
                <name><PERSON></name>
                <age>30</age>
                <email><EMAIL></email>
            </person>
            """;

        // XSD schema content
        String xsdContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
                <xs:element name="person">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="name" type="xs:string"/>
                            <xs:element name="age" type="xs:int"/>
                            <xs:element name="email" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:schema>
            """;

        XsdValidationResponse response = xsdValidationService.validateXmlAgainstXsd(xmlContent, xsdContent);

        assertNotNull(response);
        assertTrue(response.getIsValid());
        assertEquals(0, response.getErrorCount());
        assertEquals(0, response.getWarningCount());
        assertNotNull(response.getValidationTimestamp());
        assertTrue(response.getValidationSummary().contains("successfully"));
    }

    @Test
    void testInvalidXmlAgainstXsd() throws Exception {
        // Invalid XML content (missing required element)
        String xmlContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <person>
                <name>John Doe</name>
                <!-- Missing age element -->
                <email><EMAIL></email>
            </person>
            """;

        // XSD schema content
        String xsdContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
                <xs:element name="person">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="name" type="xs:string"/>
                            <xs:element name="age" type="xs:int"/>
                            <xs:element name="email" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:schema>
            """;

        XsdValidationResponse response = xsdValidationService.validateXmlAgainstXsd(xmlContent, xsdContent);

        assertNotNull(response);
        assertFalse(response.getIsValid());
        assertTrue(response.getErrorCount() > 0);
        assertNotNull(response.getErrors());
        assertFalse(response.getErrors().isEmpty());
        assertNotNull(response.getValidationTimestamp());
        assertTrue(response.getValidationSummary().contains("failed"));
    }

    @Test
    void testValidationWithRequest() throws Exception {
        XsdValidationRequest request = new XsdValidationRequest();
        
        // Valid XML content
        request.setXmlContent("""
            <?xml version="1.0" encoding="UTF-8"?>
            <book>
                <title>Test Book</title>
                <author>Test Author</author>
                <isbn>123-456-789</isbn>
            </book>
            """);

        // XSD schema content
        request.setXsdSchemaContent("""
            <?xml version="1.0" encoding="UTF-8"?>
            <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
                <xs:element name="book">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="title" type="xs:string"/>
                            <xs:element name="author" type="xs:string"/>
                            <xs:element name="isbn" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:schema>
            """);

        XsdValidationResponse response = xsdValidationService.validateXmlAgainstXsd(request);

        assertNotNull(response);
        assertTrue(response.getIsValid());
        assertEquals(0, response.getErrorCount());
        assertEquals("inline-schema", response.getSchemaName());
    }

    @Test
    void testEmptyXmlContent() {
        XsdValidationRequest request = new XsdValidationRequest();
        request.setXmlContent("");
        request.setXsdSchemaContent("<xs:schema xmlns:xs=\"http://www.w3.org/2001/XMLSchema\"></xs:schema>");

        assertThrows(IllegalArgumentException.class, () -> {
            xsdValidationService.validateXmlAgainstXsd(request);
        });
    }

    @Test
    void testMissingSchemaContent() {
        XsdValidationRequest request = new XsdValidationRequest();
        request.setXmlContent("<test>content</test>");
        // No schema content or URL provided

        assertThrows(IllegalArgumentException.class, () -> {
            xsdValidationService.validateXmlAgainstXsd(request);
        });
    }

    @Test
    void testMalformedXml() throws Exception {
        String malformedXml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <person>
                <name>John Doe
                <!-- Missing closing tag -->
            </person>
            """;

        String xsdContent = """
            <?xml version="1.0" encoding="UTF-8"?>
            <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
                <xs:element name="person">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="name" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:schema>
            """;

        XsdValidationResponse response = xsdValidationService.validateXmlAgainstXsd(malformedXml, xsdContent);

        assertNotNull(response);
        assertFalse(response.getIsValid());
        assertTrue(response.getErrorCount() > 0);
        assertNotNull(response.getErrors());
        assertTrue(response.getValidationSummary().contains("failed"));
    }
}
