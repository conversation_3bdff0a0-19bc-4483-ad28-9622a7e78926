//package com.example.demo.validation.core;
//
//import com.example.demo.validation.dto.ValidationResult;
//import com.example.demo.validation.dto.ValidationSetInfo;
//import com.helger.commons.io.resource.ClassPathResource;
//import com.helger.commons.io.resource.IReadableResource;
//import com.helger.commons.io.streamprovider.StringInputStreamProvider;
//import com.helger.diver.api.coord.DVRCoordinate;
//import com.helger.phive.api.execute.ValidationExecutionManager;
//import com.helger.phive.api.executorset.IValidationExecutorSet;
//import com.helger.phive.api.executorset.ValidationExecutorSet;
//import com.helger.phive.api.executorset.ValidationExecutorSetRegistry;
//import com.helger.phive.api.executorset.status.ValidationExecutorSetStatus;
//import com.helger.phive.api.result.ValidationResultList;
//import com.helger.phive.xml.schematron.ValidationExecutorSchematron;
//import com.helger.phive.xml.source.IValidationSourceXML;
//import com.helger.phive.xml.source.ValidationSourceXML;
//import com.helger.phive.xml.xsd.ValidationExecutorXSD;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import com.helger.xml.namespace.IIterableNamespaceContext;
//
//import java.nio.charset.StandardCharsets;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.ConcurrentHashMap;
//
///**
// * Abstract base class for PHIVE validation services
// *
// * This class provides a foundation for implementing custom validation logic
// * that can be easily extended and overridden for specific business requirements.
// *
// * Key features:
// * - Extensible validation rule registration
// * - Customizable XSD and Schematron validation
// * - Thread-safe validation execution
// * - Detailed error reporting and metrics
// * - Support for multiple validation layers
// */
//@Slf4j
//public abstract class AbstractValidationService {
//
//    @Autowired
//    protected ValidationExecutorSetRegistry<IValidationSourceXML> validationRegistry;
//
//    // Cache for validation executor sets to improve performance
//    private final Map<String, IValidationExecutorSet<IValidationSourceXML>> executorSetCache =
//        new ConcurrentHashMap<>();
//
//    /**
//     * Main validation method - can be overridden for custom logic
//     */
//    public ValidationResult validateXml(String xmlContent, String validationSetId) {
//        log.info("Starting validation with set: {}", validationSetId);
//
//        long startTime = System.currentTimeMillis();
//
//        try {
//            // Get or create validation executor set
//            IValidationExecutorSet<IValidationSourceXML> executorSet = getValidationExecutorSet(validationSetId);
//
//            if (executorSet == null) {
//                return createErrorResult("Validation executor set not found: " + validationSetId, startTime);
//            }
//
//            // Create validation source
//            IValidationSourceXML validationSource = createValidationSource(xmlContent);
//
//            // Execute validation with pre/post processing hooks
//            ValidationResultList resultList = executeValidationWithHooks(executorSet, validationSource);
//
//            // Process and return results
//            return processValidationResults(resultList, validationSetId, startTime);
//
//        } catch (Exception e) {
//            log.error("Validation failed for set: {}", validationSetId, e);
//            return createErrorResult("Validation failed: " + e.getMessage(), startTime);
//        }
//    }
//
//    /**
//     * Get validation executor set - uses cache for performance
//     */
//    protected IValidationExecutorSet<IValidationSourceXML> getValidationExecutorSet(String validationSetId) {
//        return executorSetCache.computeIfAbsent(validationSetId, id -> {
//            IValidationExecutorSet<IValidationSourceXML> executorSet = findExecutorSetById(id);
//            if (executorSet == null) {
//                // Try to create dynamically if not found
//                executorSet = createDynamicValidationSet(id);
//            }
//            return executorSet;
//        });
//    }
//
//    /**
//     * Create validation source from XML content - can be overridden
//     */
//    protected IValidationSourceXML createValidationSource(String xmlContent) {
//        return ValidationSourceXML.create(
//                new StringInputStreamProvider(xmlContent, StandardCharsets.UTF_8));
//    }
//
//    /**
//     * Execute validation with pre/post processing hooks
//     */
//    protected ValidationResultList executeValidationWithHooks(
//            IValidationExecutorSet<IValidationSourceXML> executorSet,
//            IValidationSourceXML validationSource) {
//
//        // Pre-validation hook
//        preValidationHook(executorSet, validationSource);
//
//        // Execute validation
//        ValidationResultList resultList = ValidationExecutionManager.executeValidation(
//            executorSet, validationSource);
//
//        // Post-validation hook
//        postValidationHook(executorSet, validationSource, resultList);
//
//        return resultList;
//    }
//
//    /**
//     * Pre-validation hook - override for custom logic
//     */
//    protected void preValidationHook(IValidationExecutorSet<IValidationSourceXML> executorSet,
//                                   IValidationSourceXML validationSource) {
//        log.debug("Pre-validation hook for executor set: {}", executorSet.getID());
//    }
//
//    /**
//     * Post-validation hook - override for custom logic
//     */
//    protected void postValidationHook(IValidationExecutorSet<IValidationSourceXML> executorSet,
//                                    IValidationSourceXML validationSource,
//                                    ValidationResultList resultList) {
//        log.debug("Post-validation hook for executor set: {}, results: {}",
//                 executorSet.getID(), resultList.size());
//    }
//
//    /**
//     * Process validation results - can be overridden for custom result handling
//     */
//    protected ValidationResult processValidationResults(ValidationResultList resultList,
//                                                       String validationSetId,
//                                                       long startTime) {
//
//        boolean isSuccess = resultList.containsNoError();
//        long duration = System.currentTimeMillis() - startTime;
//
//        log.info("Validation completed for set: {} - Success: {} - Duration: {}ms",
//                validationSetId, isSuccess, duration);
//
//        return ValidationResult.builder()
//            .success(isSuccess)
//            .validationSetId(validationSetId)
//            .resultList(resultList)
//            .durationMs(duration)
//            .build();
//    }
//
//    /**
//     * Create error result
//     */
//    protected ValidationResult createErrorResult(String errorMessage, long startTime) {
//        long duration = System.currentTimeMillis() - startTime;
//
//        return ValidationResult.builder()
//            .success(false)
//            .errorMessage(errorMessage)
//            .durationMs(duration)
//            .build();
//    }
//
//    /**
//     * Create dynamic validation set - override for custom creation logic
//     */
//    protected IValidationExecutorSet<IValidationSourceXML> createDynamicValidationSet(String validationSetId) {
//        log.warn("Dynamic validation set creation not implemented for: {}", validationSetId);
//        return null;
//    }
//
//    // Abstract methods that must be implemented by subclasses
//
//    /**
//     * Register custom validation rules - must be implemented
//     */
//    public abstract void registerValidationRules();
//
//    /**
//     * Create custom XSD validator - can be overridden
//     */
//    protected ValidationExecutorXSD createXSDValidator(String schemaPath) {
//        try {
//            return ValidationExecutorXSD.create(new ClassPathResource(schemaPath));
//        } catch (Exception e) {
//            log.error("Failed to create XSD validator for: {}", schemaPath, e);
//            return null;
//        }
//    }
//
//    /**
//     * Create custom Schematron validator - can be overridden
//     */
//    protected ValidationExecutorSchematron createSchematronValidator(String schematronPath) {
//        return createSchematronValidator(schematronPath, null);
//    }
//
//    /**
//     * Create custom Schematron validator with namespace context - can be overridden
//     */
//    protected ValidationExecutorSchematron createSchematronValidator(String schematronPath,
//                                                                   IIterableNamespaceContext namespaceContext) {
//        try {
//            return ValidationExecutorSchematron.createXSLT(
//                new ClassPathResource(schematronPath), namespaceContext);
//        } catch (Exception e) {
//            log.error("Failed to create Schematron validator for: {}", schematronPath, e);
//            return null;
//        }
//    }
//
//    /**
//     * Create validation executor set with multiple validators
//     */
//    protected ValidationExecutorSet<IValidationSourceXML> createValidationExecutorSet(
//            String groupId, String artifactId, String version, String displayName,
//            ValidationExecutorXSD xsdValidator,
//            ValidationExecutorSchematron... schematronValidators) {
//
//        try {
//            // Collect all non-null validators
//            List<Object> validators = new ArrayList<>();
//
//            // Add XSD validator if provided
//            if (xsdValidator != null) {
//                validators.add(xsdValidator);
//            }
//
//            // Add Schematron validators
//            for (ValidationExecutorSchematron schematronValidator : schematronValidators) {
//                if (schematronValidator != null) {
//                    validators.add(schematronValidator);
//                }
//            }
//
//            // Use PHIVE 9.2.2 compatible approach
//            try {
//                DVRCoordinate coordinate = DVRCoordinate.create(groupId, artifactId, version);
//                ValidationExecutorSetStatus status = ValidationExecutorSetStatus.createValidNow();
//
//                // Create empty ValidationExecutorSet first, then add validators
//                ValidationExecutorSet<IValidationSourceXML> executorSet =
//                    new ValidationExecutorSet<>(coordinate, displayName, status);
//
//                // Add each validator individually
//                for (Object validator : validators) {
//                    if (validator != null) {
//                        executorSet.addExecutor(validator);
//                    }
//                }
//
//                return executorSet;
//
//            } catch (Exception createException) {
//                log.error("Failed to create ValidationExecutorSet: {}:{}:{}",
//                         groupId, artifactId, version, createException);
//                return null;
//            }
//
//        } catch (Exception e) {
//            log.error("Failed to create validation executor set: {}:{}:{}",
//                     groupId, artifactId, version, e);
//            return null;
//        }
//    }
//
//    /**
//     * Register a validation executor set
//     */
//    protected void registerValidationExecutorSet(ValidationExecutorSet<IValidationSourceXML> executorSet) {
//        if (executorSet != null) {
//            validationRegistry.registerValidationExecutorSet(executorSet);
//            log.info("Registered validation executor set: {}", executorSet.getID());
//        }
//    }
//
//    /**
//     * Get all available validation sets
//     */
//    public List<ValidationSetInfo> getAvailableValidationSets() {
//        List<ValidationSetInfo> validationSets = new ArrayList<>();
//
//        for (IValidationExecutorSet<IValidationSourceXML> executorSet : validationRegistry.getAll()) {
//            validationSets.add(ValidationSetInfo.builder()
//                .id(String.valueOf(executorSet.getID()))
//                .displayName(executorSet.getDisplayName())
//                .status(executorSet.getStatus().isValidPerNow() ? "ACTIVE" : "INACTIVE")
//                .executorCount(executorSet.getCount())
//                .build());
//        }
//
//        return validationSets;
//    }
//
//    /**
//     * Clear validation cache - useful for testing or dynamic updates
//     */
//    public void clearValidationCache() {
//        executorSetCache.clear();
//        log.info("Validation executor set cache cleared");
//    }
//
//    /**
//     * Find validation executor set by ID string - works around VESID type issues
//     */
//    protected IValidationExecutorSet<IValidationSourceXML> findExecutorSetById(String executorSetId) {
//        // Try to find by exact string match first
//        for (IValidationExecutorSet<IValidationSourceXML> executorSet : validationRegistry.getAll()) {
//            String setIdString = String.valueOf(executorSet.getID());
//            if (setIdString.equals(executorSetId)) {
//                return executorSet;
//            }
//        }
//
//        // Try to parse as DVRCoordinate and match
//        try {
//            DVRCoordinate targetCoordinate = parseVESID(executorSetId);
//            for (IValidationExecutorSet<IValidationSourceXML> executorSet : validationRegistry.getAll()) {
//                // Use String comparison to avoid type issues
//                if (String.valueOf(executorSet.getID()).equals(String.valueOf(targetCoordinate))) {
//                    return executorSet;
//                }
//            }
//        } catch (Exception e) {
//            log.debug("Could not parse as DVRCoordinate: {}", executorSetId);
//        }
//
//        return null;
//    }
//
//    /**
//     * Parse VESID string into DVRCoordinate
//     * Expected format: "groupId:artifactId:version" or "groupId.artifactId.version"
//     */
//    protected DVRCoordinate parseVESID(String vesidString) {
//        if (vesidString == null || vesidString.trim().isEmpty()) {
//            throw new IllegalArgumentException("VESID string cannot be null or empty");
//        }
//
//        // Handle both colon and dot separators
//        String[] parts;
//        if (vesidString.contains(":")) {
//            parts = vesidString.split(":");
//        } else if (vesidString.contains(".")) {
//            // For backward compatibility with dot notation
//            parts = vesidString.split("\\.");
//            // Reconstruct as groupId.subgroup:artifactId:version if more than 3 parts
//            if (parts.length > 3) {
//                String groupId = String.join(".", java.util.Arrays.copyOfRange(parts, 0, parts.length - 2));
//                String artifactId = parts[parts.length - 2];
//                String version = parts[parts.length - 1];
//                try {
//                    return DVRCoordinate.create(groupId, artifactId, version);
//                } catch (Exception e) {
//                    throw new IllegalArgumentException("Failed to create DVRCoordinate from: " + vesidString, e);
//                }
//            }
//        } else {
//            throw new IllegalArgumentException("Invalid VESID format. Expected 'groupId:artifactId:version' or 'groupId.artifactId.version'");
//        }
//
//        if (parts.length != 3) {
//            throw new IllegalArgumentException("Invalid VESID format. Expected 'groupId:artifactId:version' but got: " + vesidString);
//        }
//
//        try {
//            return DVRCoordinate.create(parts[0].trim(), parts[1].trim(), parts[2].trim());
//        } catch (Exception e) {
//            throw new IllegalArgumentException("Failed to create DVRCoordinate from: " + vesidString, e);
//        }
//    }
//}
