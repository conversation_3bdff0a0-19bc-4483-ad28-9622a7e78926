package com.example.demo.service;

// Lang<PERSON>hain4j dependencies commented out until they are added to the project
/*
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentLoader;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.parser.apache.tika.ApacheTikaDocumentParser;
import dev.langchain4j.data.document.source.UrlSource;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.openai.OpenAiEmbeddingModel;
import dev.langchain4j.model.openai.OpenAiEmbeddingModelName;
import dev.langchain4j.model.openai.OpenAiTokenizer;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.chroma.ChromaEmbeddingStore;
*/
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class DocumentEmbeddingService {

    /**
     * Placeholder service for document embedding functionality
     * TODO: Implement when LangChain4j dependencies are added
     */
    public List<String> processDocumentAndStoreEmbeddings(String apiKey, String dataset, String url, String testQuestion) {
        log.info("Document embedding service called - not yet implemented");
        return Arrays.asList("Document embedding service is not yet implemented");
    }

    /*
    // Original implementation commented out until LangChain4j dependencies are added
    public List<String> processDocumentAndStoreEmbeddingsOriginal(String apiKey, String dataset, String url, String testQuestion) {
        List<String> infoList = new ArrayList<>();

        try {
            // Build ChromaDB embedding store
            ChromaEmbeddingStore embeddingStore = ChromaEmbeddingStore.builder()
                    .baseUrl("http://chromadb:8000")
                    .collectionName(dataset)
                    .build();

            // Build embedding model
            OpenAiEmbeddingModel embeddingModel = OpenAiEmbeddingModel.builder()
                    .apiKey(apiKey)
                    .modelName(OpenAiEmbeddingModelName.TEXT_EMBEDDING_ADA_002)
                    .build();

            // Document parser
            ApacheTikaDocumentParser documentParser = new ApacheTikaDocumentParser();
            Document document = DocumentLoader.load(new UrlSource(new URL(url)), documentParser);
            
            // Tokenizer and document splitter
            OpenAiTokenizer tokenizer = new OpenAiTokenizer("gpt-4o");
            DocumentSplitter splitter = DocumentSplitters.recursive(8000, 0, tokenizer);
            
            // Split document into segments
            List<TextSegment> segments = splitter.split(document);
            
            // Create embeddings and store them
            List<Embedding> embeddings = embeddingModel.embedAll(segments).content();
            embeddingStore.addAll(embeddings, segments);
            
            // Test retrieval with a question
            Embedding queryEmbedding = embeddingModel.embed(testQuestion).content();
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(1)
                    // You can add a minimum score to filter out very low relevance results:
                    // .minScore(0.7) // Example: only return matches with a score of 0.7 or higher
                    // You can also add metadata filters here if your TextSegments have metadata:
                    // .filter(Filter.from("document_url", url)) // Example: only search within this document's segments
                    .build();
            List<EmbeddingMatch<TextSegment>> relevant = embeddingStore.search(searchRequest).matches();
            EmbeddingMatch<TextSegment> embeddingMatch = relevant.get(0);
            
            // Add information about the process
            infoList.add("Document processed: " + url);
            infoList.add("Segments created: " + segments.size());
            infoList.add("Collection name: " + dataset);
            infoList.add("Test question: " + testQuestion);
            infoList.add("Score: " + embeddingMatch.score());
            infoList.add("Retrieved text: " + embeddingMatch.embedded().text().substring(0, Math.min(100, embeddingMatch.embedded().text().length())) + "...");
            
            return infoList;
            
        } catch (Exception e) {
            infoList.add("Error: " + e.getMessage());
            return infoList;
        }
    }
    */
}