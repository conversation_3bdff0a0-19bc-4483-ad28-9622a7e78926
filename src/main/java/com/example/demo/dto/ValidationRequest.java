package com.example.demo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * Request object for XML validation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationRequest {
    
    /**
     * The XML content to validate
     */
    @NotBlank(message = "XML content cannot be blank")
    private String xmlContent;
    
    /**
     * The validation executor set ID to use for validation
     */
    @NotBlank(message = "Validation executor set ID cannot be blank")
    private String validationExecutorSetId;
    
    /**
     * Optional namespace mappings for custom validation
     */
    private Map<String, String> namespaceMappings;
    
    /**
     * Optional validation set ID for custom validation
     */
    private String validationSetId;
    
    /**
     * Whether to include detailed error information
     */
    @Builder.Default
    private boolean includeDetailedErrors = true;
    
    /**
     * Whether to stop validation on first error
     */
    @Builder.Default
    private boolean stopOnFirstError = false;
    
    /**
     * Maximum number of errors to report (0 = unlimited)
     */
    @Builder.Default
    private int maxErrors = 0;
}
