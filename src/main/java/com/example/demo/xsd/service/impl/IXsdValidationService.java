package com.perennialsys.peppol.validation.xsd.service;

import com.perennialsys.peppol.validation.xsd.dto.XsdValidationRequest;
import com.perennialsys.peppol.validation.xsd.dto.XsdValidationResponse;

/**
 * Service interface for XSD validation operations.
 * Provides methods to validate XML content against XSD schemas.
 * 
 * <AUTHOR> @since 1.0
 * @version 1.0
 */
public interface IXsdValidationService {

    /**
     * Validates XML content against an XSD schema.
     * 
     * @param request The validation request containing XML content and XSD schema information
     * @return XsdValidationResponse containing validation results
     * @throws Exception if validation process encounters errors
     */
    XsdValidationResponse validateXmlAgainstXsd(XsdValidationRequest request) throws Exception;

    /**
     * Validates XML string against XSD schema content.
     * 
     * @param xmlContent The XML content to validate
     * @param xsdSchemaContent The XSD schema content
     * @return XsdValidationResponse containing validation results
     * @throws Exception if validation process encounters errors
     */
    XsdValidationResponse validateXmlAgainstXsd(String xmlContent, String xsdSchemaContent) throws Exception;

    /**
     * Validates XML string against XSD schema from URL.
     * 
     * @param xmlContent The XML content to validate
     * @param xsdSchemaUrl The URL to the XSD schema
     * @return XsdValidationResponse containing validation results
     * @throws Exception if validation process encounters errors
     */
    XsdValidationResponse validateXmlAgainstXsdUrl(String xmlContent, String xsdSchemaUrl) throws Exception;
}
