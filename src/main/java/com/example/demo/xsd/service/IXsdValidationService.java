package com.example.demo.xsd.service;


import com.example.demo.xsd.dto.XsdValidationRequest;
import com.example.demo.xsd.dto.XsdValidationResponse;

public interface IXsdValidationService {

    /**
     * Validates XML content against an XSD schema.
     * 
     * @param request The validation request containing XML content and XSD schema information
     * @return XsdValidationResponse containing validation results
     * @throws Exception if validation process encounters errors
     */
    XsdValidationResponse validateXmlAgainstXsd(XsdValidationRequest request) throws Exception;

    /**
     * Validates XML string against XSD schema content.
     * 
     * @param xmlContent The XML content to validate
     * @param xsdSchemaContent The XSD schema content
     * @return XsdValidationResponse containing validation results
     * @throws Exception if validation process encounters errors
     */
    XsdValidationResponse validateXmlAgainstXsd(String xmlContent, String xsdSchemaContent) throws Exception;

    /**
     * Validates XML string against XSD schema from URL.
     * 
     * @param xmlContent The XML content to validate
     * @param xsdSchemaUrl The URL to the XSD schema
     * @return XsdValidationResponse containing validation results
     * @throws Exception if validation process encounters errors
     */
    XsdValidationResponse validateXmlAgainstXsdUrl(String xmlContent, String xsdSchemaUrl) throws Exception;
}
