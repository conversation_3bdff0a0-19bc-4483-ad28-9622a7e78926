package com.perennialsys.peppol.validation.xsd.service.impl;

import com.helger.commons.io.resource.ClassPathResource;
import com.helger.commons.io.resource.StringInputStreamProvider;
import com.helger.commons.io.resource.URLResource;
import com.perennialsys.peppol.validation.constants.ValidationMessages;
import com.perennialsys.peppol.validation.xsd.dto.XsdValidationError;
import com.perennialsys.peppol.validation.xsd.dto.XsdValidationRequest;
import com.perennialsys.peppol.validation.xsd.dto.XsdValidationResponse;
import com.perennialsys.peppol.validation.xsd.service.IXsdValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.xml.sax.ErrorHandler;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;

import javax.xml.XMLConstants;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Service implementation for XSD validation operations.
 * Uses standard Java XML validation APIs along with Phive utilities.
 * 
 * <AUTHOR> Narayane
 * @since 1.0
 * @version 1.0
 */
@Slf4j
@Service
public class XsdValidationServiceImpl implements IXsdValidationService {

    @Override
    public XsdValidationResponse validateXmlAgainstXsd(XsdValidationRequest request) throws Exception {
        log.info("START :: CLASS :: XsdValidationServiceImpl :: METHOD :: validateXmlAgainstXsd");

        // Validate request
        if (request.getXmlContent() == null || request.getXmlContent().trim().isEmpty()) {
            throw new IllegalArgumentException(ValidationMessages.XML_NOT_NULL_OR_EMPTY);
        }

        if ((request.getXsdSchemaContent() == null || request.getXsdSchemaContent().trim().isEmpty()) &&
            (request.getXsdSchemaUrl() == null || request.getXsdSchemaUrl().trim().isEmpty())) {
            throw new IllegalArgumentException(ValidationMessages.XSD_SCHEMA_OR_URL_REQUIRED);
        }

        XsdValidationResponse response;

        // Determine validation method based on available schema information
        if (request.getXsdSchemaContent() != null && !request.getXsdSchemaContent().trim().isEmpty()) {
            response = validateXmlAgainstXsd(request.getXmlContent(), request.getXsdSchemaContent());
        } else {
            response = validateXmlAgainstXsdUrl(request.getXmlContent(), request.getXsdSchemaUrl());
        }

        // Set additional metadata
        if (request.getXsdSchemaUrl() != null) {
            response.setSchemaName(extractSchemaNameFromUrl(request.getXsdSchemaUrl()));
        } else {
            response.setSchemaName("inline-schema");
        }

        log.info("END :: CLASS :: XsdValidationServiceImpl :: METHOD :: validateXmlAgainstXsd");
        return response;
    }

    @Override
    public XsdValidationResponse validateXmlAgainstXsd(String xmlContent, String xsdSchemaContent) throws Exception {
        log.info("START :: CLASS :: XsdValidationServiceImpl :: METHOD :: validateXmlAgainstXsd(String, String)");

        XsdValidationResponse response = new XsdValidationResponse();
        response.setValidationTimestamp(LocalDateTime.now());
        response.setSchemaName("inline-schema");

        List<XsdValidationError> errors = new ArrayList<>();
        List<XsdValidationError> warnings = new ArrayList<>();

        try {
            // Create schema factory
            SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
            
            // Create schema from XSD content
            InputStream xsdInputStream = new ByteArrayInputStream(xsdSchemaContent.getBytes(StandardCharsets.UTF_8));
            Schema schema = schemaFactory.newSchema(new StreamSource(xsdInputStream));
            
            // Create validator
            Validator validator = schema.newValidator();
            
            // Set custom error handler to collect errors and warnings
            XsdErrorHandler errorHandler = new XsdErrorHandler(errors, warnings);
            validator.setErrorHandler(errorHandler);
            
            // Validate XML
            InputStream xmlInputStream = new ByteArrayInputStream(xmlContent.getBytes(StandardCharsets.UTF_8));
            validator.validate(new StreamSource(xmlInputStream));
            
            // Set validation results
            response.setIsValid(errors.isEmpty());
            response.setErrors(errors);
            response.setWarnings(warnings);
            response.setErrorCount(errors.size());
            response.setWarningCount(warnings.size());
            
            if (response.getIsValid()) {
                response.setValidationSummary("XML validation completed successfully - no errors found");
            } else {
                response.setValidationSummary(String.format("XML validation failed with %d error(s) and %d warning(s)", 
                    errors.size(), warnings.size()));
            }

        } catch (Exception e) {
            log.error("EXCEPTION :: CLASS :: XsdValidationServiceImpl :: METHOD :: validateXmlAgainstXsd :: REASON :: {}", e.getMessage());
            
            // Add the exception as a fatal error
            XsdValidationError fatalError = new XsdValidationError("FATAL", 
                "Validation process failed: " + e.getMessage());
            errors.add(fatalError);
            
            response.setIsValid(false);
            response.setErrors(errors);
            response.setWarnings(warnings);
            response.setErrorCount(errors.size());
            response.setWarningCount(warnings.size());
            response.setValidationSummary("XML validation failed due to processing error: " + e.getMessage());
        }

        log.info("END :: CLASS :: XsdValidationServiceImpl :: METHOD :: validateXmlAgainstXsd(String, String)");
        return response;
    }

    @Override
    public XsdValidationResponse validateXmlAgainstXsdUrl(String xmlContent, String xsdSchemaUrl) throws Exception {
        log.info("START :: CLASS :: XsdValidationServiceImpl :: METHOD :: validateXmlAgainstXsdUrl");

        XsdValidationResponse response = new XsdValidationResponse();
        response.setValidationTimestamp(LocalDateTime.now());
        response.setSchemaName(extractSchemaNameFromUrl(xsdSchemaUrl));

        List<XsdValidationError> errors = new ArrayList<>();
        List<XsdValidationError> warnings = new ArrayList<>();

        try {
            // Create schema factory
            SchemaFactory schemaFactory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
            
            // Create schema from URL
            Schema schema;
            if (xsdSchemaUrl.startsWith("http://") || xsdSchemaUrl.startsWith("https://")) {
                // Handle HTTP/HTTPS URLs
                URL url = new URL(xsdSchemaUrl);
                schema = schemaFactory.newSchema(url);
            } else {
                // Handle file paths or classpath resources
                if (xsdSchemaUrl.startsWith("classpath:")) {
                    String resourcePath = xsdSchemaUrl.substring("classpath:".length());
                    ClassPathResource resource = new ClassPathResource(resourcePath);
                    schema = schemaFactory.newSchema(new StreamSource(resource.getInputStream()));
                } else {
                    // Treat as file path
                    schema = schemaFactory.newSchema(new StreamSource(xsdSchemaUrl));
                }
            }
            
            // Create validator
            Validator validator = schema.newValidator();
            
            // Set custom error handler to collect errors and warnings
            XsdErrorHandler errorHandler = new XsdErrorHandler(errors, warnings);
            validator.setErrorHandler(errorHandler);
            
            // Validate XML
            InputStream xmlInputStream = new ByteArrayInputStream(xmlContent.getBytes(StandardCharsets.UTF_8));
            validator.validate(new StreamSource(xmlInputStream));
            
            // Set validation results
            response.setIsValid(errors.isEmpty());
            response.setErrors(errors);
            response.setWarnings(warnings);
            response.setErrorCount(errors.size());
            response.setWarningCount(warnings.size());
            
            if (response.getIsValid()) {
                response.setValidationSummary("XML validation completed successfully - no errors found");
            } else {
                response.setValidationSummary(String.format("XML validation failed with %d error(s) and %d warning(s)", 
                    errors.size(), warnings.size()));
            }

        } catch (Exception e) {
            log.error("EXCEPTION :: CLASS :: XsdValidationServiceImpl :: METHOD :: validateXmlAgainstXsdUrl :: REASON :: {}", e.getMessage());
            
            // Add the exception as a fatal error
            XsdValidationError fatalError = new XsdValidationError("FATAL", 
                "Validation process failed: " + e.getMessage());
            errors.add(fatalError);
            
            response.setIsValid(false);
            response.setErrors(errors);
            response.setWarnings(warnings);
            response.setErrorCount(errors.size());
            response.setWarningCount(warnings.size());
            response.setValidationSummary("XML validation failed due to processing error: " + e.getMessage());
        }

        log.info("END :: CLASS :: XsdValidationServiceImpl :: METHOD :: validateXmlAgainstXsdUrl");
        return response;
    }

    /**
     * Extracts schema name from URL for display purposes.
     */
    private String extractSchemaNameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return "unknown-schema";
        }
        
        String[] parts = url.split("/");
        if (parts.length > 0) {
            return parts[parts.length - 1];
        }
        
        return url;
    }

    /**
     * Custom error handler to collect validation errors and warnings.
     */
    private static class XsdErrorHandler implements ErrorHandler {
        private final List<XsdValidationError> errors;
        private final List<XsdValidationError> warnings;

        public XsdErrorHandler(List<XsdValidationError> errors, List<XsdValidationError> warnings) {
            this.errors = errors;
            this.warnings = warnings;
        }

        @Override
        public void warning(SAXParseException exception) throws SAXException {
            XsdValidationError warning = new XsdValidationError(
                "WARNING", 
                exception.getMessage(),
                exception.getLineNumber(),
                exception.getColumnNumber()
            );
            warnings.add(warning);
            log.warn("XSD Validation Warning: {} at line {}, column {}", 
                exception.getMessage(), exception.getLineNumber(), exception.getColumnNumber());
        }

        @Override
        public void error(SAXParseException exception) throws SAXException {
            XsdValidationError error = new XsdValidationError(
                "ERROR", 
                exception.getMessage(),
                exception.getLineNumber(),
                exception.getColumnNumber()
            );
            errors.add(error);
            log.error("XSD Validation Error: {} at line {}, column {}", 
                exception.getMessage(), exception.getLineNumber(), exception.getColumnNumber());
        }

        @Override
        public void fatalError(SAXParseException exception) throws SAXException {
            XsdValidationError error = new XsdValidationError(
                "FATAL", 
                exception.getMessage(),
                exception.getLineNumber(),
                exception.getColumnNumber()
            );
            errors.add(error);
            log.error("XSD Validation Fatal Error: {} at line {}, column {}", 
                exception.getMessage(), exception.getLineNumber(), exception.getColumnNumber());
        }
    }
}
