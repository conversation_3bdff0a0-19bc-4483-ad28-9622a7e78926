package com.example.demo.xsd.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * DTO representing a validation error or warning from XSD validation.
 * 
 * <AUTHOR>
 * @since 1.0
 * @version 1.0
 */
@Data
@ToString
@Schema(description = "XSD validation error or warning details")
public class XsdValidationError {

    /**
     * The severity level of the validation issue (ERROR, WARNING, FATAL).
     */
    @Schema(description = "Severity level of the validation issue", 
            example = "ERROR",
            allowableValues = {"ERROR", "WARNING", "FATAL"})
    private String severity;

    /**
     * The error or warning message.
     */
    @Schema(description = "Error or warning message", 
            example = "Element 'invalidElement' is not allowed")
    private String message;

    /**
     * Line number where the error occurred (if available).
     */
    @Schema(description = "Line number where the error occurred", 
            example = "15")
    private Integer lineNumber;

    /**
     * Column number where the error occurred (if available).
     */
    @Schema(description = "Column number where the error occurred", 
            example = "25")
    private Integer columnNumber;

    /**
     * XPath expression pointing to the location of the error (if available).
     */
    @Schema(description = "XPath expression pointing to the error location", 
            example = "/root/element[1]")
    private String xpath;

    /**
     * The element or attribute name that caused the error.
     */
    @Schema(description = "Element or attribute name that caused the error", 
            example = "invalidElement")
    private String elementName;

    /**
     * Additional context or details about the error.
     */
    @Schema(description = "Additional context or details about the error")
    private String context;

    /**
     * Constructor for creating an XSD validation error.
     */
    public XsdValidationError() {
    }

    /**
     * Constructor for creating an XSD validation error with basic information.
     * 
     * @param severity The severity level
     * @param message The error message
     */
    public XsdValidationError(String severity, String message) {
        this.severity = severity;
        this.message = message;
    }

    /**
     * Constructor for creating an XSD validation error with location information.
     * 
     * @param severity The severity level
     * @param message The error message
     * @param lineNumber The line number
     * @param columnNumber The column number
     */
    public XsdValidationError(String severity, String message, Integer lineNumber, Integer columnNumber) {
        this.severity = severity;
        this.message = message;
        this.lineNumber = lineNumber;
        this.columnNumber = columnNumber;
    }
}
