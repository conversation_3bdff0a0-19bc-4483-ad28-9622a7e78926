package com.example.demo.xsd.dto;

import com.perennialsys.peppol.validation.constants.ValidationMessages;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.ToString;

/**
 * Request DTO for XSD validation containing the XML content to be validated
 * and the XSD schema information.
 * 
 * <AUTHOR> @since 1.0
 * @version 1.0
 */
@Data
@ToString
@Schema(description = "Request object for XSD validation")
public class XsdValidationRequest {

    /**
     * The XML content to be validated against the XSD schema.
     * This field is mandatory and cannot be null or empty.
     */
    @NotBlank(message = ValidationMessages.XML_NOT_NULL_OR_EMPTY)
    @Schema(description = "XML content to be validated", 
            example = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><root><element>value</element></root>",
            required = true)
    private String xmlContent;

    /**
     * The XSD schema content as a string. 
     * Either this field or xsdSchemaUrl must be provided.
     */
    @Schema(description = "XSD schema content as string", 
            example = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><xs:schema xmlns:xs=\"http://www.w3.org/2001/XMLSchema\">...</xs:schema>")
    private String xsdSchemaContent;

    /**
     * URL or file path to the XSD schema.
     * Either this field or xsdSchemaContent must be provided.
     */
    @Schema(description = "URL or file path to XSD schema", 
            example = "https://example.com/schema.xsd")
    private String xsdSchemaUrl;

    /**
     * Optional namespace URI for the XML validation.
     */
    @Schema(description = "Namespace URI for XML validation", 
            example = "http://example.com/namespace")
    private String namespaceUri;

    /**
     * Optional root element name for validation.
     */
    @Schema(description = "Root element name for validation", 
            example = "Invoice")
    private String rootElementName;
}
