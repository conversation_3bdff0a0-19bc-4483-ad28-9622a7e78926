package com.example.demo.xsd.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;


@Data
@ToString
@Schema(description = "Response object for XSD validation results")
public class XsdValidationResponse {

    /**
     * Indicates whether the XML is valid against the XSD schema.
     */
    @Schema(description = "Whether the XML is valid against the XSD schema", 
            example = "true")
    private Boolean isValid;

    /**
     * List of validation errors if any.
     */
    @Schema(description = "List of validation errors")
    private List<XsdValidationError> errors;

    /**
     * List of validation warnings if any.
     */
    @Schema(description = "List of validation warnings")
    private List<XsdValidationError> warnings;

    /**
     * Total number of errors found.
     */
    @Schema(description = "Total number of errors found", 
            example = "0")
    private Integer errorCount;

    /**
     * Total number of warnings found.
     */
    @Schema(description = "Total number of warnings found", 
            example = "0")
    private Integer warningCount;

    /**
     * Validation timestamp.
     */
    @Schema(description = "Validation timestamp")
    private LocalDateTime validationTimestamp;

    /**
     * Additional validation details or summary message.
     */
    @Schema(description = "Additional validation details or summary message", 
            example = "XML validation completed successfully")
    private String validationSummary;

    /**
     * Name or identifier of the XSD schema used for validation.
     */
    @Schema(description = "Name or identifier of the XSD schema used", 
            example = "invoice-schema.xsd")
    private String schemaName;
}
