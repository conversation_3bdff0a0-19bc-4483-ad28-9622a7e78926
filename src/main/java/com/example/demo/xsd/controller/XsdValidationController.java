package com.perennialsys.peppol.validation.xsd.controller;

import com.perennialsys.peppol.validation.constants.ApiUrls;
import com.perennialsys.peppol.validation.constants.ValidationMessages;
import com.perennialsys.peppol.validation.xsd.dto.XsdValidationRequest;
import com.perennialsys.peppol.validation.xsd.dto.XsdValidationResponse;
import com.perennialsys.peppol.validation.xsd.service.IXsdValidationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for XSD validation operations.
 * Provides endpoints to validate XML content against XSD schemas.
 * 
 * <AUTHOR> Narayane
 * @since 1.0
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(ApiUrls.XSD_REQUEST_MAPPING)
@Tag(name = "XSD Validation", description = "APIs for validating XML content against XSD schemas")
public class XsdValidationController {

    @Autowired
    private IXsdValidationService xsdValidationService;

    /**
     * Validates XML content against an XSD schema.
     * Accepts a request body containing XML content and XSD schema information.
     * 
     * @param request The validation request containing XML content and XSD schema
     * @return ResponseEntity containing validation results
     */
    @Operation(
        summary = "Validate XML against XSD schema",
        description = "Validates the provided XML content against the specified XSD schema. " +
                     "The XSD schema can be provided either as inline content or as a URL/file reference."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Validation completed successfully",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = XsdValidationResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "400", 
            description = "Bad request - invalid input parameters"
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "Internal server error during validation"
        )
    })
    @PostMapping(
        value = ApiUrls.XSD_VALIDATE_XML,
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<XsdValidationResponse> validateXmlAgainstXsd(
            @Parameter(description = "XSD validation request containing XML content and schema information", required = true)
            @Valid @RequestBody XsdValidationRequest request) {

        log.info("START :: CLASS :: XsdValidationController :: METHOD :: validateXmlAgainstXsd");

        try {
            XsdValidationResponse response = xsdValidationService.validateXmlAgainstXsd(request);
            
            log.info("END :: CLASS :: XsdValidationController :: METHOD :: validateXmlAgainstXsd :: " +
                    "VALIDATION_RESULT :: {} :: ERROR_COUNT :: {} :: WARNING_COUNT :: {}", 
                    response.getIsValid(), response.getErrorCount(), response.getWarningCount());
            
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (IllegalArgumentException e) {
            log.error("VALIDATION_ERROR :: CLASS :: XsdValidationController :: METHOD :: validateXmlAgainstXsd :: REASON :: {}", e.getMessage());
            
            XsdValidationResponse errorResponse = createErrorResponse(e.getMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);

        } catch (Exception e) {
            log.error("EXCEPTION :: CLASS :: XsdValidationController :: METHOD :: validateXmlAgainstXsd :: REASON :: {}", e.getMessage(), e);
            
            XsdValidationResponse errorResponse = createErrorResponse("Internal server error during validation: " + e.getMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Validates XML string against XSD schema content (simplified endpoint).
     * Accepts XML content and XSD schema as separate request parameters.
     * 
     * @param xmlContent The XML content to validate
     * @param xsdSchemaContent The XSD schema content
     * @return ResponseEntity containing validation results
     */
    @Operation(
        summary = "Validate XML string against XSD schema content",
        description = "Simplified endpoint that validates XML content against XSD schema content provided as separate parameters."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Validation completed successfully",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = XsdValidationResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "400", 
            description = "Bad request - invalid input parameters"
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "Internal server error during validation"
        )
    })
    @PostMapping(
        value = "/validate-simple",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<XsdValidationResponse> validateXmlSimple(
            @Parameter(description = "XML content to validate", required = true)
            @RequestParam("xmlContent") 
            @NotBlank(message = ValidationMessages.XML_NOT_NULL_OR_EMPTY) String xmlContent,
            
            @Parameter(description = "XSD schema content", required = true)
            @RequestParam("xsdSchemaContent") 
            @NotBlank(message = ValidationMessages.XSD_SCHEMA_NOT_NULL_OR_EMPTY) String xsdSchemaContent) {

        log.info("START :: CLASS :: XsdValidationController :: METHOD :: validateXmlSimple");

        try {
            XsdValidationResponse response = xsdValidationService.validateXmlAgainstXsd(xmlContent, xsdSchemaContent);
            
            log.info("END :: CLASS :: XsdValidationController :: METHOD :: validateXmlSimple :: " +
                    "VALIDATION_RESULT :: {} :: ERROR_COUNT :: {} :: WARNING_COUNT :: {}", 
                    response.getIsValid(), response.getErrorCount(), response.getWarningCount());
            
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("EXCEPTION :: CLASS :: XsdValidationController :: METHOD :: validateXmlSimple :: REASON :: {}", e.getMessage(), e);
            
            XsdValidationResponse errorResponse = createErrorResponse("Validation failed: " + e.getMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Validates XML string against XSD schema from URL.
     * 
     * @param xmlContent The XML content to validate
     * @param xsdSchemaUrl The URL to the XSD schema
     * @return ResponseEntity containing validation results
     */
    @Operation(
        summary = "Validate XML string against XSD schema from URL",
        description = "Validates XML content against an XSD schema loaded from the specified URL or file path."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "Validation completed successfully",
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                schema = @Schema(implementation = XsdValidationResponse.class)
            )
        ),
        @ApiResponse(
            responseCode = "400", 
            description = "Bad request - invalid input parameters"
        ),
        @ApiResponse(
            responseCode = "500", 
            description = "Internal server error during validation"
        )
    })
    @PostMapping(
        value = "/validate-url",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<XsdValidationResponse> validateXmlAgainstUrl(
            @Parameter(description = "XML content to validate", required = true)
            @RequestParam("xmlContent") 
            @NotBlank(message = ValidationMessages.XML_NOT_NULL_OR_EMPTY) String xmlContent,
            
            @Parameter(description = "URL or file path to XSD schema", required = true)
            @RequestParam("xsdSchemaUrl") 
            @NotBlank(message = ValidationMessages.XSD_SCHEMA_NOT_NULL_OR_EMPTY) String xsdSchemaUrl) {

        log.info("START :: CLASS :: XsdValidationController :: METHOD :: validateXmlAgainstUrl :: XSD_URL :: {}", xsdSchemaUrl);

        try {
            XsdValidationResponse response = xsdValidationService.validateXmlAgainstXsdUrl(xmlContent, xsdSchemaUrl);
            
            log.info("END :: CLASS :: XsdValidationController :: METHOD :: validateXmlAgainstUrl :: " +
                    "VALIDATION_RESULT :: {} :: ERROR_COUNT :: {} :: WARNING_COUNT :: {}", 
                    response.getIsValid(), response.getErrorCount(), response.getWarningCount());
            
            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            log.error("EXCEPTION :: CLASS :: XsdValidationController :: METHOD :: validateXmlAgainstUrl :: REASON :: {}", e.getMessage(), e);
            
            XsdValidationResponse errorResponse = createErrorResponse("Validation failed: " + e.getMessage());
            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Creates an error response for exception handling.
     * 
     * @param errorMessage The error message
     * @return XsdValidationResponse with error information
     */
    private XsdValidationResponse createErrorResponse(String errorMessage) {
        XsdValidationResponse response = new XsdValidationResponse();
        response.setIsValid(false);
        response.setErrorCount(1);
        response.setWarningCount(0);
        response.setValidationSummary(errorMessage);
        response.setValidationTimestamp(java.time.LocalDateTime.now());
        return response;
    }
}
