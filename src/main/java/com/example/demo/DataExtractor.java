//package com.example.demo;
//
//import dev.langchain4j.model.chat.ChatLanguageModel;
//import dev.langchain4j.model.openai.OpenAiChatModel;
//
//import java.util.Map;
//
//public class DataExtractor {
//
//    public static void main(String[] args) {
//        ChatLanguageModel model = OpenAiChatModel.builder()
//                .apiKey("********************************************************\n")
//                .modelName("gpt-4")
//                .build();
//
//        String inputText = "<PERSON> was born on July 5th, 1990 and lives at 1234 Pine St, New York.";
//
//        String prompt = """
//            Extract the following fields from the text:
//            - Name
//            - Date of Birth
//            - Address
//
//            Text:
//            %s
//
//            Return the result as valid JSON with keys: name, date_of_birth, address.
//        """.formatted(inputText);
//
//        String response = model.generate(prompt);
//        System.out.println("Extracted JSON:\n" + response);
//    }
//}
