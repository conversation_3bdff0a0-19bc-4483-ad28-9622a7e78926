package com.example.demo.constants;


public class ValidationMessages {

    /**
     * Error message indicating that the document must not be null or empty.
     */
    public static final String DOCUMENT_NOT_NULL_OR_EMPTY = "Document must not be null or empty";

    /**
     * Error message indicating that the PINT specification must not be null or empty.
     */
    public static final String PINT_SPEC_NOT_NULL_OR_EMPTY = "Pint specification must not be null or empty";

    /**
     * Error message indicating that the document type must not be null or empty.
     */
    public static final String DOCUMENT_TYPE_NOT_NULL_OR_EMPTY = "Document type must not be null or empty";

    /**
     * Error message indicating that the version must not be null or empty.
     */
    public static final String VERSION_NOT_NULL_OR_EMPTY = "Version must not be null or empty";

    /**
     * Error message indicating that the XML string must not be null or empty.
     */
    public static final String XML_NOT_NULL_OR_EMPTY = "XML String must not be null or empty";

    /**
     * Error message indicating that the XSD schema must not be null or empty.
     */
    public static final String XSD_SCHEMA_NOT_NULL_OR_EMPTY = "XSD schema must not be null or empty";

    /**
     * Error message indicating that either XSD schema content or XSD schema URL must be provided.
     */
    public static final String XSD_SCHEMA_OR_URL_REQUIRED = "Either XSD schema content or XSD schema URL must be provided";

}
