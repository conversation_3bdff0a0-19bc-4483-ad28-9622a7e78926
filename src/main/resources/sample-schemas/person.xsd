<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://example.com/person"
           xmlns:tns="http://example.com/person"
           elementFormDefault="qualified">

    <!-- Person element definition -->
    <xs:element name="person" type="tns:PersonType"/>

    <!-- Person complex type -->
    <xs:complexType name="PersonType">
        <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="1"/>
            <xs:element name="age" type="xs:int" minOccurs="1"/>
            <xs:element name="email" type="tns:EmailType" minOccurs="0"/>
            <xs:element name="address" type="tns:AddressType" minOccurs="0"/>
            <xs:element name="phone" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
        <xs:attribute name="id" type="xs:string" use="required"/>
    </xs:complexType>

    <!-- Email type with pattern restriction -->
    <xs:simpleType name="EmailType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[^@]+@[^@]+\.[^@]+"/>
        </xs:restriction>
    </xs:simpleType>

    <!-- Address complex type -->
    <xs:complexType name="AddressType">
        <xs:sequence>
            <xs:element name="street" type="xs:string"/>
            <xs:element name="city" type="xs:string"/>
            <xs:element name="state" type="xs:string" minOccurs="0"/>
            <xs:element name="zipCode" type="xs:string"/>
            <xs:element name="country" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>

</xs:schema>
