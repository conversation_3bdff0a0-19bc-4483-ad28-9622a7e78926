spring.application.name=demo

# Disable JPA Auto-Configuration (since we're using MongoDB)
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

# MongoDB Configuration
spring.data.mongodb.uri=mongodb://localhost:27017/test

# Additional MongoDB configuration
spring.data.mongodb.auto-index-creation=false
spring.data.mongodb.authentication-database=admin
# spring.data.mongodb.username=admin
# spring.data.mongodb.password=password

# Control database initialization (true for dev, false for prod)
app.db.initialize=false

# Add these lines for more detailed logging
#logging.level.org.springframework.data.mongodb=DEBUG
#logging.level.org.mongodb=DEBUG

# Enhanced logging
logging.level.root=INFO
logging.level.com.example.demo=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.scheduling=DEBUG

# Configure Config Server

# Remove this line if present
# spring.cloud.config.enabled=false
#spring.config.import=configserver:http://localhost:8888
#spring.cloud.config.uri=http://localhost:8888
#spring.cloud.config.name=${spring.application.name}

# Disable scheduling
spring.task.scheduling.enabled=false

# Disable actuator endpoints if needed
#management.endpoints.web.exposure.include=health,info
#management.endpoint.health.show-details=never
